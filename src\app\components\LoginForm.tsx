import React, { useState } from "react";
import useFirebaseAuth from "../hooks/useFirebaseAuth";
import PasswordReset from "./PasswordReset";
import Image from "next/image";

const LoginForm: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSignUp, setIsSignUp] = useState(false);
  const [displayName, setDisplayName] = useState("");
  const [showPasswordReset, setShowPasswordReset] = useState(false);
  const { signIn, signUp, loading, error, clearError } = useFirebaseAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    if (isSignUp) {
      await signUp(email, password, displayName);
    } else {
      await signIn(email, password);
    }
  };

  const toggleMode = () => {
    setIsSignUp(!isSignUp);
    clearError();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !loading) {
      e.preventDefault();
      handleSubmit(e as unknown as React.FormEvent);
    }
  };

  if (showPasswordReset) {
    return <PasswordReset onCancel={() => setShowPasswordReset(false)} />;
  }

  return (
    <div className="max-w-md w-full p-8 border border-gray-200 rounded-xl shadow-sm bg-white">
      <div>
        <Image
          src="/logo/logo.png"
          alt="Logo"
          width={100}
          height={100}
          className="mx-auto p-4 rounded-full"
        />
      </div>
      <h2 className="text-2xl font-bold mb-4 text-center">
        {isSignUp ? "Sign Up" : "Welcome Back"}
      </h2>

      {error && (
        <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {isSignUp && (
          <div className="mb-4">
            <label
              htmlFor="displayName"
              className="block mb-1 font-medium font-semibold text-gray-700"
            >
              Name
            </label>
            <input
              type="text"
              id="displayName"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              required={isSignUp}
              placeholder="your name"
              className="w-full px-5 py-4 rounded-full outline-none bg-gray-100"
            />
          </div>
        )}

        <div className="mb-4">
          <label
            htmlFor="email"
            className="block mb-1 font-medium font-semibold text-gray-700"
          >
            Email
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="<EMAIL>"
            required
            className="w-full px-5 py-4 rounded-full outline-none bg-gray-100 "
          />
        </div>

        <div className="mb-4">
          <label
            htmlFor="password"
            className="block mb-1 font-medium font-semibold text-gray-700"
          >
            Password
          </label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Enter your password"
            required
            className="w-full px-5 py-4 rounded-full outline-none bg-gray-100"
          />
        </div>
        {!isSignUp && (
          <button
            onClick={() => {
              clearError();
              setShowPasswordReset(true);
            }}
            disabled={loading}
            className="text-blue-300 hover:underline text-md font-medium flex justify-end w-full mb-4 pr-5 pointer-cursor"
          >
            Forgot your password?
          </button>
        )}
        <button
          type="submit"
          disabled={loading}
          className={`w-full py-4 px-3 mt-4 rounded-full text-white font-semibold ${
            loading
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-blue-500 hover:bg-blue-600 transition-colors"
          }`}
        >
          {loading ? "Processing..." : isSignUp ? "SIGN UP" : "LOGIN"}
        </button>
      </form>

      <div className="mt-4 flex justify-between items-center">
        <button
          onClick={toggleMode}
          disabled={loading}
          className="text-blue-300 hover:underline text-md font-medium flex justify-center w-full my-4"
        >
          {isSignUp
            ? "Already have an account? Login"
            : "Need an account? Sign Up"}
        </button>
      </div>
    </div>
  );
};

export default LoginForm;




