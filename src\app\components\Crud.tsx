import React, { useState, useEffect } from "react";
import { FaTrashAlt } from "react-icons/fa";
import { FiEdit } from "react-icons/fi";
import useFirebaseData from "../hooks/useFirebaseData";
import AddProduct from "../components/Addproduct";
import EditProduct from "../components/EditProduct";
import { GrCaretNext, GrCaretPrevious } from "react-icons/gr";
import { Item, ProductData } from "../types";

type CrudProps = {
  isActive: boolean;
};

const Crud: React.FC<CrudProps> = ({ isActive }) => {
  const { items, loading, error, deleteItem, updateItem } = useFirebaseData();
  const [editingItem, setEditingItem] = useState<ProductData | null>(null);
  const [isAdding, setIsAdding] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [newlyAddedItem, setNewlyAddedItem] = useState<Item | null>(null);

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);

  const handleDeleteItem = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this item?")) {
      await deleteItem(id);
    }
  };

  const handleEditClick = (item: ProductData) => {
    setEditingItem(item);
  };

  const handleSaveEdit = async () => {
    if (!editingItem) return;

    const updatedData = {
      productName: editingItem.productName,
      price: editingItem.price,
      imageUrl: editingItem.imageUrl,
      productType: editingItem.productType,
      productDetails: editingItem.productDetails,
      filterPrice: editingItem.filterPrice,
      originalPrice: editingItem.originalPrice,
      discountPercent: editingItem.discountPercent,
      storeRating: editingItem.storeRating,
      productReviewRating: editingItem.productReviewRating,
      soldCount: editingItem.soldCount,
      productLink1: editingItem.productLink1,
      productLink2: editingItem.productLink2,
      shopeeLink: editingItem.shopeeLink,
      lazadaLink: editingItem.lazadaLink,
    };

    try {
      if (!editingItem?.id) throw new Error("No item selected for update");
      await updateItem(editingItem.id, updatedData);
      console.log("Item updated successfully:", updatedData);
      setEditingItem(null);
    } catch (error) {
      console.error("Error updating item:", error);
    }
  };

  // Reset newly added item when search query changes
  useEffect(() => {
    if (searchQuery) {
      setNewlyAddedItem(null);
    }
  }, [searchQuery]);

  // Clear the newly added item highlight after 5 seconds
  useEffect(() => {
    if (newlyAddedItem) {
      const timer = setTimeout(() => {
        setNewlyAddedItem(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [newlyAddedItem]);

  // Filter and sort items based on search query
  const filteredItems = items
    .filter(
      (item) => {
        const typedItem = item as unknown as ProductData;
        return (
          typedItem.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (typedItem.productType &&
            typedItem.productType.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (typedItem.productDetails &&
            typedItem.productDetails
              .toLowerCase()
              .includes(searchQuery.toLowerCase())) ||
          (typedItem.price && typedItem.price.toString().includes(searchQuery)) ||
          (typedItem.filterPrice && typedItem.filterPrice.toString().includes(searchQuery))
        );
      }
    )
    .sort((a, b) => {
      // If we have a newly added item, it should always be at the top
      if (newlyAddedItem && a.id === newlyAddedItem.id) return -1;
      if (newlyAddedItem && b.id === newlyAddedItem.id) return 1;

      // Otherwise, sort by timestamp (latest first)
      const aTyped = a as unknown as ProductData;
      const bTyped = b as unknown as ProductData;
      const dateA = new Date(aTyped.timestamp || 0).getTime();
      const dateB = new Date(bTyped.timestamp || 0).getTime();
      return dateB - dateA;
    });

  // Pagination logic
  const totalPages = Math.ceil(filteredItems.length / rowsPerPage);
  const paginatedItems = filteredItems.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  const handleRowsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setRowsPerPage(Number(e.target.value));
    setCurrentPage(1); // Reset to the first page
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  if (loading) {
    return <div className="text-center text-gray-500">Loading...</div>;
  }

  // ใช้ isActive ตามต้องการ เช่น
  if (!isActive) return null;

  return (
    <div className="p-4">
      {error && <div className="text-red-500 mb-4">Error: {error}</div>}
      <div className="mb-4 flex items-center justify-between">
        <button
          onClick={() => setIsAdding(true)}
          className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
        >
          Add Product
        </button>
        <input
          type="text"
          placeholder="Search products..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="p-2 px-4 border border-gray-300 rounded-full w-2/3 outline-none focus:ring focus:ring-blue-200"
        />
        <select
          value={rowsPerPage}
          onChange={handleRowsPerPageChange}
          className="p-2 border border-gray-300 rounded-md outline-none"
        >
          <option value={10}>10</option>
          <option value={20}>20</option>
          <option value={50}>50</option>
        </select>
      </div>

      {isAdding && (
        <AddProduct
          onClose={() => {
            setIsAdding(false);
          }}
          onProductAdded={(newItem) => {
            // Set the newly added item
            setNewlyAddedItem(newItem);
            // Reset to first page to show the newly added item
            setCurrentPage(1);
            // Scroll to top to ensure the new item is visible
            window.scrollTo(0, 0);
          }}
        />
      )}
      {paginatedItems.length === 0 ? (
        <p className="text-gray-500">No products found.</p>
      ) : (
        <>
          <table className="table-auto w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100 text-center">
                <th className="border border-gray-300 px-4 py-2">No.</th>
                <th className="border border-gray-300 px-4 py-2">
                  Product Name
                </th>
                <th className="border border-gray-300 px-4 py-2">Price</th>
                <th className="border border-gray-300 px-4 py-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedItems.map((item, index) => {
                const typedItem = item as unknown as ProductData;
                return (
                  <tr
                    key={typedItem.id}
                    className={`hover:bg-gray-50 ${newlyAddedItem && typedItem.id === newlyAddedItem.id ? 'bg-green-50 animate-pulse' : ''}`}
                  >
                    <td className="border border-gray-300 px-4 py-2 text-center align-top">
                      {(currentPage - 1) * rowsPerPage + index + 1}
                      {newlyAddedItem && typedItem.id === newlyAddedItem.id && (
                        <span className="ml-2 text-xs text-green-600 font-semibold">NEW</span>
                      )}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 align-top">
                      {typedItem.productName}
                      {typedItem.shopeeLink && (
                        <a
                          href={typedItem.shopeeLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-orange-500 hover:text-orange-600"
                          title="View on Shopee"
                        >
                          Shopee
                        </a>
                      )}
                      {typedItem.lazadaLink && (
                        <a
                          href={typedItem.lazadaLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-500 hover:text-blue-600"
                          title="View on Lazada"
                        >
                          Lazada
                        </a>
                      )}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-left align-top hidden">
                      {typedItem.productType || "N/A"}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-left align-top hidden ">
                      {typedItem.productDetails || "N/A"}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-left align-top hidden">
                      {typedItem.filterPrice || "N/A"}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-left align-top hidden ">
                      {typedItem.timestamp || "N/A"}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-left align-top w-[180px] ">
                      {typedItem.price || "N/A"}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-center space-x-4 align-top">
                      <button
                        onClick={() => handleEditClick(typedItem)}
                        className="text-blue-500 hover:text-blue-600 cursor-pointer"
                        title="Edit"
                      >
                        <FiEdit size={18} />
                      </button>
                      <button
                        onClick={() => handleDeleteItem(typedItem.id!)}
                        className="text-red-500 hover:text-red-600 cursor-pointer"
                        title="Delete"
                      >
                        <FaTrashAlt size={18} />
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          <div className="flex justify-center items-center mt-4 space-x-4">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:text-black "
            >
              <GrCaretPrevious size={18} />
            </button>
            <span>
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:text-black "
            >
              <GrCaretNext size={18} />
            </button>
          </div>
        </>
      )}

      {editingItem && (
        <EditProduct
          editingItem={editingItem}
          setEditingItem={setEditingItem}
          onSave={handleSaveEdit}
          onCancel={() => setEditingItem(null)}
        />
      )}
    </div>
  );
};

export default Crud;
