import React from "react";
import { Item } from "../types";
import useProductTypes from "../hooks/useProductTypes";

interface EditProductProps {
  editingItem: Item;
  setEditingItem: React.Dispatch<React.SetStateAction<Item | null>>;
  onSave: () => Promise<void>;
  onCancel: () => void;
}

const EditProduct: React.FC<EditProductProps> = ({
  editingItem,
  setEditingItem,
  onSave,
  onCancel,
}) => {
  const { productTypes, loading: loadingProductTypes, error: productTypesError } = useProductTypes();

  return (
    <div className="fixed inset-0 bg-black/40 backdrop-blur-2xs flex justify-center items-center">
      <div className="bg-white p-6 rounded shadow-lg w-full max-w-2xl">
        <h3 className="text-xl font-bold mb-4">Edit Product</h3>
        <form className="space-y-4">
          <div>
            <label className="block mb-1 text-sm font-semibold text-gray-700">
              Image URL:
            </label>
            <input
              type="text"
              name="imageUrl"
              value={editingItem.imageUrl}
              onChange={(e) =>
                setEditingItem({ ...editingItem, imageUrl: e.target.value })
              }
              className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
            />
          </div>
          <div>
            <label className="block mb-1 text-sm font-semibold text-gray-700">
              ประเภทสินค้า :
            </label>
            {loadingProductTypes ? (
              <p>Loading product types...</p>
            ) : productTypesError ? (
              <p className="text-red-500">Error loading product types: {productTypesError}</p>
            ) : (
              <select
                name="productType"
                value={editingItem.productType}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    productType: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              >
                <option value="" disabled>
                  เลือกประเภทสินค้า
                </option>
                {productTypes.map((type) => (
                  <option key={type.id} value={type.name}>
                    {type.name}
                  </option>
                ))}
              </select>
            )}
          </div>
          <div>
            <label className="block mb-1 text-sm font-semibold text-gray-700">
              ชื่อสินค้า :
            </label>
            <input
              type="text"
              name="productName"
              value={editingItem.productName}
              onChange={(e) =>
                setEditingItem({
                  ...editingItem,
                  productName: e.target.value,
                })
              }
              className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
            />
          </div>
          <div>
            <label className="block mb-1 text-sm font-semibold text-gray-700">
              รายละเอียดสินค้า :
            </label>
            <textarea
              name="productDetails"
              value={editingItem.productDetails}
              onChange={(e) =>
                setEditingItem({
                  ...editingItem,
                  productDetails: e.target.value,
                })
              }
              className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                ราคาขาย :
              </label>
              <input
                type="text"
                name="price"
                value={editingItem.price || ""}
                onChange={(e) =>
                  setEditingItem({ ...editingItem, price: e.target.value })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                ราคาเดิม :
              </label>
              <input
                type="text"
                name="originalPrice"
                value={editingItem.originalPrice || ""}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    originalPrice: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                เปอร์เซนต์ที่ลด :
              </label>
              <input
                type="text"
                name="discountPercent"
                value={editingItem.discountPercent || ""}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    discountPercent: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                ราคาไว้กรอง :
              </label>
              <input
                type="number"
                name="filterPrice"
                value={editingItem.filterPrice}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    filterPrice: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                คะแนนร้านค้า :
              </label>
              <input
                type="number"
                name="storeRating"
                value={editingItem.storeRating}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    storeRating: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                จำนวนรีวิวสินค้า :
              </label>
              <input
                type="number"
                name="productReviewRating"
                value={editingItem.productReviewRating}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    productReviewRating: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                จำนวนที่ขายได้ :
              </label>
              <input
                type="number"
                name="soldCount"
                value={editingItem.soldCount}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    soldCount: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                Product Link 1:
              </label>
              <input
                type="text"
                name="productLink1"
                value={editingItem.productLink1}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    productLink1: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                Shopee Link:
              </label>
              <input
                type="text"
                name="shopeeLink"
                value={editingItem.shopeeLink || ""}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    shopeeLink: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                Product Link 2:
              </label>
              <input
                type="text"
                name="productLink2"
                value={editingItem.productLink2}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    productLink2: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                Lazada Link:
              </label>
              <input
                type="text"
                name="lazadaLink"
                value={editingItem.lazadaLink || ""}
                onChange={(e) =>
                  setEditingItem({
                    ...editingItem,
                    lazadaLink: e.target.value,
                  })
                }
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 bg-red-400 text-white rounded-md hover:bg-red-500"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={onSave}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditProduct;
