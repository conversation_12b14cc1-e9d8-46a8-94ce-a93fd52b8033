export interface Item {
  id?: string;
  productType: string;
  imageUrl: string;
  productName: string;
  productDetails: string;
  storeRating: string;
  productReviewRating: string;
  soldCount: string;
  productLink1: string;
  productLink2: string;
  shopeeLink: string;
  lazadaLink: string;
  price: string;
  filterPrice: string;
  originalPrice: string;
  discountPercent: string;
  timestamp: string;
}

// Alias for Item to maintain compatibility with existing code
export type ProductData = Item;
