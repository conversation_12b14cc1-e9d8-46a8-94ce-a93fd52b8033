import { useState, useEffect } from 'react';
import { 
  User, 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  sendPasswordResetEmail,
  updateProfile,
  EmailAuthProvider,
  reauthenticateWithCredential
} from 'firebase/auth';
import { auth } from '../firebase/config';
import { handleFirebaseError } from '../firebase/errorCodes';

interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

export const useFirebaseAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null
  });

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setAuthState(prev => ({
        ...prev,
        user,
        loading: false
      }));
    });

    // Cleanup subscription
    return () => unsubscribe();
  }, []);

  // Clear error
  const clearError = () => {
    setAuthState(prev => ({ ...prev, error: null }));
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string, displayName?: string) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      
      // Update profile with display name if provided
      if (displayName && userCredential.user) {
        await updateProfile(userCredential.user, { displayName });
      }
      
      return true;
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      setAuthState(prev => ({ ...prev, error: errorMessage }));
      return false;
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));
      await signInWithEmailAndPassword(auth, email, password);
      return true;
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      setAuthState(prev => ({ ...prev, error: errorMessage }));
      return false;
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  };

  // Sign out
  const logOut = async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));
      await signOut(auth);
      return true;
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      setAuthState(prev => ({ ...prev, error: errorMessage }));
      return false;
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));
      await sendPasswordResetEmail(auth, email);
      return true;
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      setAuthState(prev => ({ ...prev, error: errorMessage }));
      return false;
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  };

  // Update user profile
  const updateUserProfile = async (displayName: string) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));
      
      if (auth.currentUser) {
        await updateProfile(auth.currentUser, { displayName });
        // Update local state to reflect changes
        setAuthState(prev => ({ 
          ...prev, 
          user: auth.currentUser 
        }));
      } else {
        throw new Error('No user is currently signed in');
      }
      
      return true;
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      setAuthState(prev => ({ ...prev, error: errorMessage }));
      return false;
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  };

  // Reauthenticate user (needed for sensitive operations)
  const reauthenticate = async (password: string) => {
    try {
      setAuthState(prev => ({ ...prev, loading: true, error: null }));
      
      if (auth.currentUser && auth.currentUser.email) {
        const credential = EmailAuthProvider.credential(
          auth.currentUser.email,
          password
        );
        await reauthenticateWithCredential(auth.currentUser, credential);
      } else {
        throw new Error('No user is currently signed in or email is missing');
      }
      
      return true;
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      setAuthState(prev => ({ ...prev, error: errorMessage }));
      return false;
    } finally {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  };

  return {
    user: authState.user,
    loading: authState.loading,
    error: authState.error,
    signUp,
    signIn,
    logOut,
    resetPassword,
    updateUserProfile,
    reauthenticate,
    clearError
  };
};

export default useFirebaseAuth;
