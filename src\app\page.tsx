"use client";

import React, { useState, useCallback } from 'react';
import Crud from './components/Crud';
import LoginForm from './components/LoginForm';
import UserProfile from './components/UserProfile';
import useFirebaseAuth from './hooks/useFirebaseAuth';
import ProductPage from './product/page';
import useInactivityTimeout from './hooks/useInactivityTimeout';
import InactivityWarning from './components/InactivityWarning';

const App = () => {
  const { user, loading, logOut } = useFirebaseAuth();
  const [activeTab, setActiveTab] = useState<'items' | 'profile' | 'lists'>('items');

  // Handle automatic logout after inactivity
  const handleInactivityTimeout = useCallback(() => {
    if (user) {
      logOut();
    }
  }, [user, logOut]);

  // Use the inactivity timeout hook
  const { isWarningVisible, resetTimer } = useInactivityTimeout({
    onTimeout: handleInactivityTimeout,
    timeoutMinutes: 15, // Logout after 15 minutes of inactivity
    warningMinutes: 1   // Show warning 1 minute before logout
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <h2 className="text-xl font-semibold">Loading...</h2>
      </div>
    );
  }

  return (
    <div className='flex flex-col min-h-screen bg-gray-100 '>
      {/* Show inactivity warning if visible */}
      {user && isWarningVisible && (
        <InactivityWarning onContinue={resetTimer} warningMinutes={1} />
      )}

      {user ? (
        <div className='max-w-5/6 w-full mx-auto'>
          <div className="flex justify-between items-center mb-5 mt-2 p-3 bg-gray-200 rounded">
            <p className="text-gray-800">Welcome, {user.displayName || user.email}!</p>
            <div className="flex gap-2">
              <button
                onClick={logOut}
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
              >
                Logout
              </button>

            </div>
          </div>

          <div className="flex border-b border-gray-200 mb-5">
            <button
              className={`px-5 py-2.5 border-b-2 transition-colors ${activeTab === 'items'
                ? 'border-blue-500 text-blue-500 font-semibold'
                : 'border-transparent hover:bg-gray-100'}`}
              onClick={() => setActiveTab('items')}
            >
              Product Management
            </button>
            <button
              className={`px-5 py-2.5 border-b-2 transition-colors ${activeTab === 'lists'
                ? 'border-blue-500 text-blue-500 font-semibold'
                : 'border-transparent hover:bg-gray-100'}`}
              onClick={() => setActiveTab('lists')}
            >
              Product Lists
            </button>
            <button
              className={`px-5 py-2.5 border-b-2 transition-colors ${activeTab === 'profile'
                ? 'border-blue-500 text-blue-500 font-semibold'
                : 'border-transparent hover:bg-gray-100'}`}
              onClick={() => setActiveTab('profile')}
            >
              Profile
            </button>
          </div>

          {activeTab === 'items' ? (
            <Crud isActive={activeTab === 'items'} />
          ) : activeTab === 'lists' ? (
            <ProductPage />
          ) : (
            <UserProfile />
          )}

        </div>
      ) : (
        <div className="flex justify-center items-center min-h-screen bg-blue-200">
          <LoginForm />
        </div>
      )}
    </div>
  );
};

export default App;