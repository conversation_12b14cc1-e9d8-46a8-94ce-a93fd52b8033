import React, { useState } from "react";
import useFirebaseData from "../hooks/useFirebaseData"; // นำเข้า useFirebaseData
import useProductTypes from "../hooks/useProductTypes";
import { Item } from "../types";
import { FaPlus } from "react-icons/fa";

const AddProduct: React.FC<{
  onClose: () => void;
  onProductAdded?: (newItem: Item) => void;
}> = ({ onClose, onProductAdded }) => {
  const [formData, setFormData] = useState<Omit<Item, "id">>({
    imageUrl: "",
    productType: "",
    productName: "",
    productDetails: "",
    storeRating: "",
    productReviewRating: "",
    soldCount: "",
    productLink1: "",
    productLink2: "",
    shopeeLink: "",
    lazadaLink: "",
    price: "",
    filterPrice: "",
    originalPrice: "",
    discountPercent: "",
    timestamp: "",
  });

  // State for custom product type input
  const [customProductType, setCustomProductType] = useState<string>("");
  const [showCustomInput, setShowCustomInput] = useState<boolean>(false);
  const [saveTypeMessage, setSaveTypeMessage] = useState<{text: string, type: 'success' | 'error'} | null>(null);

  const { addItem } = useFirebaseData();
  const {
    productTypes,
    loading: loadingProductTypes,
    error: productTypesError,
    refetch: refetchProductTypes,
    addProductType,
  } = useProductTypes();

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    // Handle product type selection
    if (name === "productType") {
      setShowCustomInput(false);
      setFormData({ ...formData, [name]: value });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  // Handle custom product type input
  const handleCustomProductTypeChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { value } = e.target;
    setCustomProductType(value);
    // Update the formData with the custom value
    setFormData({ ...formData, productType: value });
  };

  // Handle saving a new product type
  const handleSaveProductType = async () => {
    if (!customProductType.trim()) {
      setSaveTypeMessage({
        text: "Please enter a product type name",
        type: "error"
      });
      return;
    }

    try {
      const result = await addProductType(customProductType);

      if (result.success) {
        setSaveTypeMessage({
          text: "Product type saved successfully!",
          type: "success"
        });

        // Clear the message after 3 seconds
        setTimeout(() => {
          setSaveTypeMessage(null);
        }, 3000);

        // Refresh the product types list
        await refetchProductTypes();

        // Update the form data with the new product type and hide the custom input
        setFormData({ ...formData, productType: customProductType });
        setShowCustomInput(false);
      } else {
        // Handle permission denied error specifically
        if (result.error && result.error.includes('permission-denied')) {
          // If permission denied, still use the custom type for this form
          // but inform the user it wasn't saved to the database
          setSaveTypeMessage({
            text: "Permission denied: You can still use this product type for this product, but it won't be saved for future use.",
            type: "error"
          });

          // Don't hide the custom input, but still use the value
          setFormData({ ...formData, productType: customProductType });
        } else {
          setSaveTypeMessage({
            text: result.error || "Failed to save product type",
            type: "error"
          });
        }
      }
    } catch (error) {
      console.error("Error saving product type:", error);
      setSaveTypeMessage({
        text: "An unexpected error occurred",
        type: "error"
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const currentTimestamp = new Date().toISOString();
    const productData = { ...formData, timestamp: currentTimestamp };

    try {
      // Use the hook's addItem function
      const newItem = await addItem(productData);

      if (newItem) {
        console.log("Item added successfully:", newItem);

        // Close the form first
        onClose();

        // Notify parent component about the new item
        if (onProductAdded) {
          // Ensure we're passing the complete item with ID to the parent
          onProductAdded(newItem);
        }
      } else {
        console.error("Failed to add item");
      }
    } catch (error) {
      console.error("Error adding document: ", error);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/40 backdrop-blur-2xs z-50 ">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-2xl">
        <h2 className="text-2xl font-semibold mb-4">Add Product</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block mb-1 text-sm font-semibold text-gray-700">
              Image URL:
            </label>
            <input
              type="text"
              name="imageUrl"
              value={formData.imageUrl}
              onChange={handleChange}
              required
              className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
            />
          </div>
          <div>
            <label className="block mb-1 text-sm font-semibold text-gray-700">
              ประเภทสินค้า :
            </label>
            {loadingProductTypes ? (
              <p>Loading product types...</p>
            ) : productTypesError ? (
              <div>
                <p className="text-red-500 mb-2">
                  Error loading product types: {productTypesError}
                </p>
                <button
                  type="button"
                  onClick={() => refetchProductTypes()}
                  className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                >
                  Retry
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <select
                  name="productType"
                  value={formData.productType}
                  onChange={handleChange}
                  required
                  className="flex-1 p-2 rounded-md text-sm outline-none bg-gray-100"
                >
                  <option value="" disabled>
                    เลือกประเภทสินค้า
                  </option>
                  {productTypes.map((type) => (
                    <option key={type.id} value={type.name}>
                      {type.name}
                    </option>
                  ))}
                </select>
                <button
                  type="button"
                  onClick={() => {
                    setShowCustomInput(true);
                  }}
                  className="p-2 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center justify-center"
                  title="Add New Product Type"
                >
                  <FaPlus size={16} />
                </button>
              </div>
            )}
            {showCustomInput && (
              <div className="mt-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    placeholder="Enter custom product type"
                    value={customProductType}
                    onChange={handleCustomProductTypeChange}
                    required={showCustomInput}
                    className="flex-1 p-2 rounded-md text-sm outline-none bg-gray-100"
                  />
                  <button
                    type="button"
                    onClick={handleSaveProductType}
                    className="px-3 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600"
                  >
                    Save Type
                  </button>
                </div>
                {saveTypeMessage && (
                  <div
                    className={`mt-2 p-2 rounded text-sm ${
                      saveTypeMessage.type === 'success'
                        ? 'bg-green-100 text-green-700'
                        : 'bg-red-100 text-red-700'
                    }`}
                  >
                    {saveTypeMessage.text}
                  </div>
                )}
              </div>
            )}
          </div>
          <div>
            <label className="block mb-1 text-sm font-semibold text-gray-700">
              ชื่อสินค้า :
            </label>
            <input
              type="text"
              name="productName"
              value={formData.productName}
              onChange={handleChange}
              required
              className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
            />
          </div>
          <div>
            <label className="block mb-1 text-sm font-semibold text-gray-700">
              รายละเอียดสินค้า :
            </label>
            <textarea
              name="productDetails"
              value={formData.productDetails}
              onChange={handleChange}
              required
              className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                ราคาขาย :
              </label>
              <input
                type="text"
                name="price"
                value={formData.price}
                onChange={handleChange}
                required
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                ราคาเดิม :
              </label>
              <input
                type="text"
                name="originalPrice"
                value={formData.originalPrice}
                onChange={handleChange}
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                เปอร์เซนต์ที่ลด :
              </label>
              <input
                type="number"
                name="discountPercent"
                value={formData.discountPercent}
                onChange={handleChange}
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                ราคาไว้กรอง :
              </label>
              <input
                type="number"
                name="filterPrice"
                value={formData.filterPrice}
                onChange={handleChange}
                required
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                คะแนนร้านค้า :
              </label>
              <input
                type="number"
                name="storeRating"
                value={formData.storeRating}
                onChange={handleChange}
                required
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                จำนวนรีวิวสินค้า :
              </label>
              <input
                type="number"
                name="productReviewRating"
                value={formData.productReviewRating}
                onChange={handleChange}
                required
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                จำนวนที่ขายได้ :
              </label>
              <input
                type="number"
                name="soldCount"
                value={formData.soldCount}
                onChange={handleChange}
                required
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                Product Link 1:
              </label>
              <input
                type="text"
                name="productLink1"
                value={formData.productLink1}
                onChange={handleChange}
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                Shopee Link:
              </label>
              <input
                type="text"
                name="shopeeLink"
                value={formData.shopeeLink}
                onChange={handleChange}
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                Product Link 2:
              </label>
              <input
                type="text"
                name="productLink2"
                value={formData.productLink2}
                onChange={handleChange}
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-semibold text-gray-700">
                Lazada Link:
              </label>
              <input
                type="text"
                name="lazadaLink"
                value={formData.lazadaLink}
                onChange={handleChange}
                className="w-full p-2 rounded-md text-sm outline-none bg-gray-100"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-red-400 text-white rounded-md hover:bg-red-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            >
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddProduct;
