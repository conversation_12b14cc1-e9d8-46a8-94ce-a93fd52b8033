import { FirebaseError } from 'firebase/app';
import { FirestoreError } from 'firebase/firestore';

// Firestore error codes and messages
export const firestoreErrorMessages: Record<string, string> = {
  'permission-denied': 'You don\'t have permission to access the requested resource.',
  'not-found': 'The requested document was not found.',
  'already-exists': 'The document already exists.',
  'resource-exhausted': 'Quota exceeded or rate limit reached.',
  'failed-precondition': 'The operation was rejected because the system is not in a state required for the operation.',
  'aborted': 'The operation was aborted.',
  'out-of-range': 'Operation was attempted past the valid range.',
  'unimplemented': 'Operation is not implemented or not supported.',
  'internal': 'Internal error.',
  'unavailable': 'The service is currently unavailable.',
  'data-loss': 'Unrecoverable data loss or corruption.',
  'unauthenticated': 'The request does not have valid authentication credentials.'
};

// Authentication error codes and messages
export const authErrorMessages: Record<string, string> = {
  'auth/email-already-in-use': 'The email address is already in use by another account.',
  'auth/invalid-email': 'Email address is not valid.',
  'auth/operation-not-allowed': 'Email/password accounts are not enabled.',
  'auth/weak-password': 'Password is too weak.',
  'auth/user-disabled': 'User account has been disabled.',
  'auth/user-not-found': 'No user found with this email.',
  'auth/wrong-password': 'Password is invalid.',
  'auth/invalid-credential': 'The credential is malformed or has expired.',
  'auth/invalid-verification-code': 'The verification code is invalid.',
  'auth/missing-verification-code': 'The verification code is missing.',
  'auth/quota-exceeded': 'Quota exceeded.',
  'auth/rejected-credential': 'The request contains malformed or mismatching credentials.',
  'auth/too-many-requests': 'Too many requests. Try again later.',
  'auth/unauthorized-domain': 'The domain of this URL is not authorized.',
  'auth/requires-recent-login': 'This operation requires recent authentication. Please log in again.',
  'auth/provider-already-linked': 'The provider is already linked to the user.',
  'auth/credential-already-in-use': 'This credential is already associated with a different user account.',
  'auth/invalid-action-code': 'The action code is invalid. This can happen if the code is malformed, expired, or has already been used.',
  'auth/invalid-persistence-type': 'The specified persistence type is invalid.',
  'auth/invalid-phone-number': 'The format of the phone number provided is incorrect.',
  'auth/missing-phone-number': 'The phone number is missing.',
  'auth/missing-verification-id': 'The verification ID is missing.',
  'auth/app-deleted': 'This instance of FirebaseApp has been deleted.',
  'auth/account-exists-with-different-credential': 'An account already exists with the same email address but different sign-in credentials.',
  'auth/network-request-failed': 'A network error occurred. Please check your connection and try again.',
  'auth/popup-blocked': 'The popup was blocked by the browser.',
  'auth/popup-closed-by-user': 'The popup was closed by the user before finalizing the operation.',
  'auth/cancelled-popup-request': 'The popup request was cancelled by the user.',
  'auth/invalid-user-token': 'The user\'s credential is no longer valid. The user must sign in again.',
  'auth/user-token-expired': 'The user\'s credential has expired. The user must sign in again.',
  'auth/web-storage-unsupported': 'This browser is not supported or 3rd party cookies and data may be disabled.',
  'auth/invalid-api-key': 'Your API key is invalid, please check you have copied it correctly.',
  'auth/app-not-authorized': 'This app is not authorized to use Firebase Authentication with the provided API key.'
};

// Helper function to handle Firebase errors
export const handleFirebaseError = (error: unknown): string => {
  if (error instanceof FirebaseError) {
    const errorCode = error.code;
    
    // Check if it's an auth error
    if (errorCode.startsWith('auth/')) {
      return authErrorMessages[errorCode] || `Authentication error: ${errorCode}`;
    }
    
    // Check if it's a Firestore error
    if (error instanceof FirestoreError) {
      return firestoreErrorMessages[errorCode] || `Firestore error: ${errorCode}`;
    }
    
    // Generic Firebase error
    return `Firebase error: ${errorCode}`;
  }
  
  // Handle non-Firebase errors
  return error instanceof Error ? error.message : 'An unknown error occurred';
};
