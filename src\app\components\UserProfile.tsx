"use client";

import React, { useState } from 'react';
import useFirebaseAuth from '../hooks/useFirebaseAuth';

const UserProfile: React.FC = () => {
  const { user, error, loading, updateUserProfile, clearError } = useFirebaseAuth();
  const [displayName, setDisplayName] = useState(user?.displayName || '');
  const [isEditing, setIsEditing] = useState(false);
  const [updateSuccess, setUpdateSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    setUpdateSuccess(false);

    if (displayName.trim()) {
      const success = await updateUserProfile(displayName);
      if (success) {
        setIsEditing(false);
        setUpdateSuccess(true);

        // Hide success message after 3 seconds
        setTimeout(() => {
          setUpdateSuccess(false);
        }, 3000);
      }
    }
  };

  if (!user) {
    return <div className="p-4 text-gray-700">Please log in to view your profile.</div>;
  }

  return (
    <div className="max-w-2xl mx-auto p-6 border border-gray-200 rounded-lg shadow-sm my-5">
      <h2 className="text-xl font-semibold mb-4">User Profile</h2>

      {error && (
        <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4">
          {error}
        </div>
      )}

      {updateSuccess && (
        <div className="bg-green-50 text-green-700 p-3 rounded-md mb-4">
          Profile updated successfully!
        </div>
      )}

      <div className="mt-5">
        <div className="flex items-center mb-4">
          <span className="font-semibold w-32">Email:</span>
          <span className="flex-1 text-gray-700">{user.email}</span>
        </div>

        <div className="flex items-center mb-4">
          <span className="font-semibold w-32">Display Name:</span>
          {isEditing ? (
            <form onSubmit={handleSubmit} className="flex-1 flex flex-col">
              <input
                type="text"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-3"
              />
              <div className="flex gap-3">
                <button
                  type="submit"
                  disabled={loading}
                  className={`px-4 py-2 rounded-md text-white font-medium ${loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-500 hover:bg-green-600 transition-colors'}`}
                >
                  {loading ? 'Saving...' : 'Save'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setIsEditing(false);
                    setDisplayName(user.displayName || '');
                    clearError();
                  }}
                  className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          ) : (
            <div className="flex items-center">
              <span className="flex-1 text-gray-700">{user.displayName || 'Not set'}</span>
              <button
                onClick={() => setIsEditing(true)}
                className="ml-3 px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
              >
                Edit
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
