"use client";

import React, { useState } from 'react';
import useFirebaseAuth from '../hooks/useFirebaseAuth';

interface PasswordResetProps {
  onCancel: () => void;
}

const PasswordReset: React.FC<PasswordResetProps> = ({ onCancel }) => {
  const [email, setEmail] = useState('');
  const [resetSent, setResetSent] = useState(false);
  const { resetPassword, loading, error, clearError } = useFirebaseAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    if (email.trim()) {
      const success = await resetPassword(email);
      if (success) {
        setResetSent(true);
      }
    }
  };

  if (resetSent) {
    return (
      <div className="max-w-lg mx-auto text-center p-5 bg-green-50 rounded-md mb-5">
        <h3 className="text-lg font-semibold text-green-800 mb-2">Password Reset Email Sent</h3>
        <p className="text-green-700 mb-4">Check your email for instructions to reset your password.</p>
        <button
          onClick={onCancel}
          className="mt-3 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Back to Login
        </button>
      </div>
    );
  }

  return (
    <div className="min-w-md mx-auto p-10 border border-gray-200 rounded-lg shadow-sm bg-white">
      <h3 className="text-xl font-semibold mb-4">Reset Password</h3>

      {error && (
        <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="reset-email" className="block mb-1 font-medium text-gray-700">Email</label>
          <input
            type="email"
            id="reset-email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="flex gap-3 mt-5">
          <button
            type="submit"
            disabled={loading}
            className={`flex-1 py-2 px-4 rounded-md text-white font-medium ${loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600 transition-colors'}`}
          >
            {loading ? 'Sending...' : 'Send Reset Email'}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 py-2 px-4 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default PasswordReset;
