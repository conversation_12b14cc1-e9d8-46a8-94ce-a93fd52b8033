"use client";

import React, { ReactNode } from 'react';
import useFirebaseAuth from '../hooks/useFirebaseAuth';
import LoginForm from './LoginForm';

interface ProtectedRouteProps {
  children: ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading } = useFirebaseAuth();

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <h2 className="text-xl font-semibold">Loading...</h2>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="max-w-md mx-auto mt-8">
        <h2 className="text-xl font-semibold mb-2 text-center">Authentication Required</h2>
        <p className="text-gray-600 mb-6 text-center">Please log in to access this page.</p>
        <LoginForm />
      </div>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
