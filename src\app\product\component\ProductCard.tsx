import Image from "next/image";
import Link from "next/link";
import React from "react";
import { FaStar } from "react-icons/fa";
import { Item } from "../../types";

interface ProductCardProps {
  product: Item;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  return (
    <div className="flex flex-row gap-4">
      <div className="flex flex-col justify-center p-2 gap-2 bg-white rounded shadow-sm w-[170px] sm:w-sm pb-4 sm:gap-4 ">
        <Image
          src={product.imageUrl}
          alt={product.productName}
          width={400}
          height={400}
          className="mx-auto w-[150px] h-auto sm:w-full sm:h-full"
        />
        <h1 className="block sm:hidden text-[12px] sm:text-base text-gray-600">
          {product.productName.length > 45
            ? `${product.productName.slice(0, 45)}...`
            : product.productName}
        </h1>
        <h1 className="hidden sm:block text-base text-gray-600">
          {product.productName}
        </h1>
        {/* <p className="text-gray-800">{product.productDetails}</p> */}
        <div className="flex flex-row gap-2 text-[12px] sm:text-base">
          <p className="flex flex-row items-center justify-center gap-1 text-gray-800 border border-yellow-300 rounded-sm bg-yellow-100 px-1">
            <FaStar className="text-yellow-400 " />
            {product.storeRating}
          </p>
          <p className="border-x-1 border-gray-300 px-3 text-gray-800 hidden sm:block">
            {Number(product.productReviewRating) >= 1_000_000
              ? (Number(product.productReviewRating) / 1_000_000).toFixed(1) +
                "ล้าน"
              : Number(product.productReviewRating) >= 1000
              ? (Number(product.productReviewRating) / 1000).toFixed(1) + "พัน"
              : product.productReviewRating}{" "}
            รีวิว
          </p>
          <p className="text-gray-800">
            ขายแล้ว{" "}
            <span className="">
              {Number(product.soldCount) >= 1_000_000
                ? (Number(product.soldCount) / 1_000_000).toFixed(1) + "ล้าน"
                : Number(product.soldCount) >= 1000
                ? (Number(product.soldCount) / 1000).toFixed(1) + "พัน"
                : product.soldCount}
            </span>{" "}
            ชิ้น
          </p>
        </div>
        <div className="flex flex-rows gap-1 sm:gap-4">
          <p className="text-gray-800 text-[12px] sm:text-base ">
            ราคา : <span className="text-red-500">{product.price}</span>
          </p>
          <p className="text-gray-800 text-[10px] sm:text-base ">
            <span className="text-gray-500 line-through">
              {product.originalPrice}
            </span>
          </p>
          {product.discountPercent && (
            <p className="text-gray-800 bg-orange-100 px-[1px] rounded text-[10px] sm:text-base sm:px-1 ">
              <span className="text-red-500">-{product.discountPercent}%</span>
            </p>
          )}
        </div>
        {(product.shopeeLink || product.lazadaLink) && (
          <p className="text-gray-800 text-[12px] sm:hidden">
            ซื้อได้ที่ :{" "}
            {product.shopeeLink && (
              <Link
                href={product.shopeeLink}
                className="text-orange-500 px-1 rounded"
              >
                shopee
              </Link>
            )}
            {product.lazadaLink && (
              <Link
                href={product.lazadaLink}
                className="bg-blue-600 text-white px-1 rounded"
              >
                Lazada
              </Link>
            )}
          </p>
        )}
        <p className="flex flex-row items-center gap-4 text-gray-800 hidden sm:flex">
          ซื้อได้ที่ :{" "}
          {product.shopeeLink && (
            <Image
              src="/logo/shopee-logo.webp"
              alt="Shopee"
              width={70}
              height={60}
              className="cursor-pointer rounded-full"
              onClick={() => window.open(product.shopeeLink)}
            />
          )}
          {product.lazadaLink && (
            <Image
              src="/lazada-logo.jpg"
              alt="Lazada"
              width={80}
              height={60}
              className="cursor-pointer rounded-full"
              onClick={() => window.open(product.lazadaLink)}
            />
          )}
        </p>
      </div>
    </div>
  );
};

export default ProductCard;
