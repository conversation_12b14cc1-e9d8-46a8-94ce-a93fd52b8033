import { useEffect, useState } from 'react';
import { db, auth } from '../firebase/config';
import { collection, getDocs, addDoc } from 'firebase/firestore';
import { handleFirebaseError } from '../firebase/errorCodes';
import { onAuthStateChanged } from 'firebase/auth';

interface ProductType {
  id: string;
  name: string;
}

const useProductTypes = () => {
  const [productTypes, setProductTypes] = useState<ProductType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  // Check authentication state
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setIsAuthenticated(!!user);
    });

    return () => unsubscribe();
  }, []);

  const fetchProductTypes = async () => {
    if (!auth.currentUser) {
      setLoading(false);
      setError("Authentication required to access product types");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const productTypesCollectionRef = collection(db, 'productTypes');
      const data = await getDocs(productTypesCollectionRef);
      const typesData = data.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as ProductType[];

      setProductTypes(typesData);
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      console.error('Error fetching product types:', errorMessage);

      if (errorMessage.includes('permission-denied')) {
        setError('You do not have permission to access product types. Please ensure you are logged in with the correct account.');
      } else {
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch product types when authenticated
    if (isAuthenticated) {
      fetchProductTypes();
    }
  }, [isAuthenticated]);

  const addProductType = async (typeName: string) => {
    if (!auth.currentUser) {
      setError("Authentication required to add product types");
      return { success: false, error: "Authentication required" };
    }

    // Check if the product type already exists
    const existingType = productTypes.find(type => type.name.toLowerCase() === typeName.toLowerCase());
    if (existingType) {
      return { success: false, error: "Product type already exists" };
    }

    try {
      setError(null);
      const productTypesCollectionRef = collection(db, 'productTypes');

      // Add the new product type to Firestore
      const docRef = await addDoc(productTypesCollectionRef, { name: typeName });

      // Create a new product type object with the document ID
      const newProductType = { id: docRef.id, name: typeName };

      // Update the local state with the new product type
      setProductTypes(prevTypes => [...prevTypes, newProductType]);

      console.log("New product type added:", newProductType);

      return { success: true, productType: newProductType };
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      console.error("Error adding product type:", errorMessage);

      if (errorMessage.includes('permission-denied')) {
        setError('You do not have permission to add product types. Please ensure you are logged in with the correct account.');
      } else {
        setError(errorMessage);
      }

      return { success: false, error: errorMessage };
    }
  };

  return {
    productTypes,
    loading,
    error,
    refetch: fetchProductTypes,
    addProductType
  };
};

export default useProductTypes;