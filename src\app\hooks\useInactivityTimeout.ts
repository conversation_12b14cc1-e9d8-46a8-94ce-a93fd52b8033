import { useEffect, useState, useCallback, useRef } from 'react';

interface UseInactivityTimeoutProps {
  onTimeout: () => void;
  timeoutMinutes?: number;
  warningMinutes?: number;
}

/**
 * A custom hook that tracks user inactivity and triggers a callback after a specified timeout period.
 * 
 * @param onTimeout - Function to call when the inactivity timeout is reached
 * @param timeoutMinutes - Number of minutes of inactivity before timeout (default: 15)
 * @param warningMinutes - Number of minutes before timeout to show warning (default: 1)
 * @returns Object containing isWarningVisible and resetTimer function
 */
const useInactivityTimeout = ({
  onTimeout,
  timeoutMinutes = 15,
  warningMinutes = 1
}: UseInactivityTimeoutProps) => {
  const [isWarningVisible, setIsWarningVisible] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const warningRef = useRef<NodeJS.Timeout | null>(null);
  
  // Convert minutes to milliseconds
  const timeoutMs = timeoutMinutes * 60 * 1000;
  const warningMs = (timeoutMinutes - warningMinutes) * 60 * 1000;
  
  // Reset both timers
  const resetTimer = useCallback(() => {
    // Clear existing timers
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    
    if (warningRef.current) {
      clearTimeout(warningRef.current);
      warningRef.current = null;
    }
    
    // Hide warning if it's visible
    setIsWarningVisible(false);
    
    // Set new timers
    warningRef.current = setTimeout(() => {
      setIsWarningVisible(true);
    }, warningMs);
    
    timeoutRef.current = setTimeout(() => {
      onTimeout();
    }, timeoutMs);
  }, [onTimeout, timeoutMs, warningMs]);
  
  // Set up event listeners for user activity
  useEffect(() => {
    // Initialize timers
    resetTimer();
    
    // Define events to listen for
    const events = [
      'mousedown', 'mousemove', 'keypress', 
      'scroll', 'touchstart', 'click', 'keydown'
    ];
    
    // Event handler to reset the timer
    const handleUserActivity = () => {
      resetTimer();
    };
    
    // Add event listeners
    events.forEach(event => {
      window.addEventListener(event, handleUserActivity);
    });
    
    // Clean up event listeners and timers
    return () => {
      events.forEach(event => {
        window.removeEventListener(event, handleUserActivity);
      });
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      if (warningRef.current) {
        clearTimeout(warningRef.current);
      }
    };
  }, [resetTimer]);
  
  return { isWarningVisible, resetTimer };
};

export default useInactivityTimeout;
